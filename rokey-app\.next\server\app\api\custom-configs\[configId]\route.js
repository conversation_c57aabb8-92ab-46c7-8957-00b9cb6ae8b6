(()=>{var e={};e.id=763,e.ids=[763],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var s=r(34386),o=r(44999);async function i(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73307:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>c,GET:()=>p,OPTIONS:()=>d});var o=r(96559),i=r(48088),n=r(37719),u=r(32190),a=r(2507);async function c(e,{params:t}){let r=(0,a.x)(),{configId:s}=await t;if(!s)return u.NextResponse.json({error:"Configuration ID is required"},{status:400});try{let{error:e,count:t}=await r.from("custom_api_configs").delete({count:"exact"}).eq("id",s);if(e){if("23503"===e.code)return u.NextResponse.json({error:"Failed to delete custom API configuration because it is still in use.",details:"Ensure no API keys are associated with this configuration before deleting."},{status:409});return u.NextResponse.json({error:"Failed to delete custom API configuration",details:e.message},{status:500})}if(0===t)return u.NextResponse.json({error:"Custom API configuration not found or already deleted."},{status:404});return u.NextResponse.json({message:"Custom API configuration deleted successfully"},{status:200})}catch(e){return u.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e,{params:t}){let r=(0,a.x)(),{configId:s}=await t;if(!s||"string"!=typeof s)return u.NextResponse.json({error:"Invalid configuration ID provided."},{status:400});try{let{data:e,error:t}=await r.from("custom_api_configs").select("id, name, created_at, updated_at, user_id, routing_strategy, routing_strategy_params").eq("id",s).single();if(t){if("PGRST116"===t.code)return u.NextResponse.json({error:"Configuration not found or you do not have permission to view it."},{status:404});return u.NextResponse.json({error:"Failed to fetch custom API configuration.",details:t.message},{status:500})}if(!e)return u.NextResponse.json({error:"Configuration not found."},{status:404});return u.NextResponse.json(e,{status:200})}catch(e){return u.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}async function d(){return u.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let l=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/custom-configs/[configId]/route",pathname:"/api/custom-configs/[configId]",filename:"route",bundlePath:"app/api/custom-configs/[configId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\custom-configs\\[configId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:g}=l;function m(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(73307));module.exports=s})();