(()=>{var e={};e.id=8923,e.ids=[8923],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{x:()=>i});var s=t(34386),o=t(44999);async function i(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){try{e.set({name:r,value:t,...s})}catch(e){}},remove(r,t){try{e.set({name:r,value:"",...t})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72059:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{DELETE:()=>p});var o=t(96559),i=t(48088),a=t(37719),n=t(32190),u=t(2507);async function p(e,{params:r}){let t=(0,u.x)(),{apiKeyId:s,roleName:o}=await r;if(!s||!o)return n.NextResponse.json({error:"API Key ID and Role Name are required"},{status:400});try{let{data:e,error:r}=await t.from("api_keys").select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `).eq("id",s).single();if(r||!e)return n.NextResponse.json({error:"API Key not found or failed to fetch its details for authorization"},{status:404});let i=e.custom_api_configs?.user_id;if(i&&"00000000-0000-0000-0000-000000000000"!==i)return n.NextResponse.json({error:"Forbidden. You do not own the configuration this API key belongs to."},{status:403});let{error:a,count:u}=await t.from("api_key_role_assignments").delete({count:"exact"}).eq("api_key_id",s).eq("role_name",o);if(a)return n.NextResponse.json({error:"Failed to unassign role from API key",details:a.message},{status:500});if(0===u)return n.NextResponse.json({error:"Role assignment not found for this API key or already unassigned."},{status:404});return n.NextResponse.json({message:`Role '${o}' unassigned successfully from API key ${s}`},{status:200})}catch(e){return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/keys/[apiKeyId]/roles/[roleName]/route",pathname:"/api/keys/[apiKeyId]/roles/[roleName]",filename:"route",bundlePath:"app/api/keys/[apiKeyId]/roles/[roleName]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\[apiKeyId]\\roles\\[roleName]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function y(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,3410],()=>t(72059));module.exports=s})();